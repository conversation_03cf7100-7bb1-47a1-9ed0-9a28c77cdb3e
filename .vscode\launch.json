{"version": "0.2.0", "configurations": [{"name": "Run nnUNet_train", "type": "python", "request": "launch", "program": "${workspaceFolder}/run_training.py", "args": ["Dataset101_HNREOS_CT2AT_CavityOral_Constrict", "3d_fullres_T", "0", "-tr", "nnUNetTrainer"], "cwd": "C:/ARTDaemon/distribute/nnUNETDatav2.HN/nnUNet_raw/Dataset101_HNREOS_CT2AT_CavityOral_Constrict", "env": {"PATH": "${workspaceFolder};${env:PATH}"}, "console": "integratedTerminal", "justMyCode": false}, {"name": "Run nnUNet_plan_and_preprocess", "type": "python", "request": "launch", "program": "${workspaceFolder}/plan_and_preprocess_entrypoints.py", "args": ["-d", "101", "-c", "3d_fullres", "3d_fullres_T", "--verify_dataset_integrity", "--verbose", "--clean"], "cwd": "C:/ARTDaemon/distribute/nnUNETDatav2.HN/nnUNet_raw/Dataset101_HNREOS_CT2AT_CavityOral_Constrict", "env": {"PATH": "${workspaceFolder};${env:PATH}"}, "console": "integratedTerminal", "justMyCode": false}, {"name": "Run nnUNet_predict", "type": "python", "request": "launch", "program": "${workspaceFolder}/predict_simple.py", "args": ["-i", "c:/test/nnunet/MNI_T1c2Mets_Merge_d1_3d_fullres_T_local_SSL/predict/Input/", "-o", "c:/test/nnunet/MNI_T1c2Mets_Merge_d1_3d_fullres_T_local_SSL/predict/Output/", "-m", "C:/ARTDaemon/distribute/nnUNETV2.BMs/nnUNet_results/Dataset001_BrainMets/nnUNetTrainer__nnUNetPlans__3d_fullres_T_local_SSL", "-f", "4", "-chk", "checkpoint_best.pth"], "cwd": "C:/test/nnunet/MNI_T1c2Mets_Merge_d1_3d_fullres_T_local_SSL/predict", "env": {"PATH": "${workspaceFolder};${env:PATH}"}, "console": "integratedTerminal", "justMyCode": false}, {"name": "WholeBreast_plan_and_preprocess", "type": "python", "request": "launch", "program": "${workspaceFolder}/plan_and_preprocess_entrypoints.py", "args": ["-d", "151", "-c", "2d", "--verify_dataset_integrity", "--verbose", "--clean"], "cwd": "C:/ARTDaemon/distribute/nnUNETDatav2.sandbox/nnUNet_raw/Dataset151_GP_image2WholeBreast", "env": {"PATH": "${workspaceFolder};${env:PATH}"}, "console": "integratedTerminal", "justMyCode": false}, {"name": "WholeBreast_train", "type": "python", "request": "launch", "program": "${workspaceFolder}/run_training.py", "args": ["Dataset151_GP_image2WholeBreast", "2d", "0", "-tr", "nnUNetTrainer"], "cwd": "C:/ARTDaemon/distribute/nnUNETDatav2.sandbox/nnUNet_raw/Dataset151_GP_image2WholeBreast", "env": {"PATH": "${workspaceFolder};${env:PATH}"}, "console": "integratedTerminal", "justMyCode": false}, {"name": "AE2d_plan_and_preprocess", "type": "python", "request": "launch", "program": "${workspaceFolder}/plan_and_preprocess_entrypoints.py", "args": ["-d", "251", "-c", "2d", "--verify_dataset_integrity", "--verbose", "--clean"], "cwd": "C:/ARTDaemon/distribute/nnUNETDatav2.sandbox/nnUNet_raw/Dataset251_GP_image2image", "env": {"PATH": "${workspaceFolder};${env:PATH}"}, "console": "integratedTerminal", "justMyCode": false}, {"name": "AE2d_train", "type": "python", "request": "launch", "program": "${workspaceFolder}/run_training.py", "args": ["Dataset251_GP_image2image", "2d_AE", "0", "-tr", "nnUNetTrainer"], "cwd": "C:/ARTDaemon/distribute/nnUNETDatav2.sandbox/nnUNet_raw/Dataset251_GP_image2image", "env": {"PATH": "${workspaceFolder};${env:PATH}"}, "console": "integratedTerminal", "justMyCode": false}, {"name": "AE2d_predict", "type": "python", "request": "launch", "program": "${workspaceFolder}/predict_simple.py", "args": ["-i", "C:/ARTDaemon/distribute/nnUNETDatav2.sandbox/nnUNet_raw/Dataset251_GP_image2image/imagesTs", "-o", "C:/ARTDaemon/distribute/nnUNETDatav2.sandbox/nnUNet_raw/Dataset251_GP_image2image/predictsTs_AE2d", "-m", "C:/ARTDaemon/distribute/nnUNETDatav2.sandbox/nnUNet_trained_models/Dataset251_GP_image2image/nnUNetTrainer__nnUNetPlans__2d_AE", "-f", "0", "-chk", "checkpoint_best.pth", "-nps", "1", "-npp", "1", "--disable_tta"], "cwd": "C:/ARTDaemon/distribute/nnUNETDatav2.sandbox/nnUNet_raw/Dataset251_GP_image2image", "env": {"PATH": "${workspaceFolder};${env:PATH}"}, "console": "integratedTerminal", "justMyCode": false}, {"name": "image_plan_and_preprocess", "type": "python", "request": "launch", "program": "${workspaceFolder}/plan_and_preprocess_entrypoints.py", "args": ["-d", "251", "-c", "2d", "--verify_dataset_integrity", "--verbose", "--clean"], "cwd": "C:/ARTDaemon/distribute/nnUNETDatav2.sandbox/nnUNet_raw/Dataset251_GP_image2image", "env": {"PATH": "${workspaceFolder};${env:PATH}"}, "console": "integratedTerminal", "justMyCode": false}, {"name": "image_train", "type": "python", "request": "launch", "program": "${workspaceFolder}/run_training.py", "args": ["Dataset251_GP_image2image", "2d", "0", "-tr", "nnUNetTrainer"], "cwd": "C:/ARTDaemon/distribute/nnUNETDatav2.sandbox/nnUNet_raw/Dataset251_GP_image2image", "env": {"PATH": "${workspaceFolder};${env:PATH}"}, "console": "integratedTerminal", "justMyCode": false}, {"name": "image_predict", "type": "python", "request": "launch", "program": "${workspaceFolder}/predict_simple.py", "args": ["-i", "C:/ARTDaemon/distribute/nnUNETDatav2.sandbox/nnUNet_raw/Dataset251_GP_image2image/imagesTs", "-o", "C:/ARTDaemon/distribute/nnUNETDatav2.sandbox/nnUNet_raw/Dataset251_GP_image2image/predictsTs", "-m", "C:/ARTDaemon/distribute/nnUNETDatav2.sandbox/nnUNet_trained_models/Dataset251_GP_image2image/nnUNetTrainer__nnUNetPlans__2d", "-f", "0", "-chk", "checkpoint_best.pth", "-nps", "1", "-npp", "1", "--disable_tta"], "cwd": "C:/ARTDaemon/distribute/nnUNETDatav2.sandbox/nnUNet_raw/Dataset251_GP_image2image", "env": {"PATH": "${workspaceFolder};${env:PATH}"}, "console": "integratedTerminal", "justMyCode": false}, {"name": "dose3d_plan_and_preprocess", "type": "python", "request": "launch", "program": "${workspaceFolder}/plan_and_preprocess_entrypoints.py", "args": ["-d", "635", "-c", "3d_fullres", "--verify_dataset_integrity", "--verbose", "--clean"], "cwd": "C:/ARTDaemon/distribute/nnUNETDatav2.dose_GP/nnUNet_raw/Dataset635_GP_CT_Rx2dose_NormRx", "env": {"PATH": "${workspaceFolder};${env:PATH}"}, "console": "integratedTerminal", "justMyCode": false}, {"name": "dose3d_train", "type": "python", "request": "launch", "program": "${workspaceFolder}/run_training.py", "args": ["Dataset635_GP_CT_Rx2dose_NormRx", "3d_fullres", "0", "-tr", "nnUNetTrainerNoMirroring"], "cwd": "C:/ARTDaemon/distribute/nnUNETDatav2.dose_GP/nnUNet_raw/Dataset635_GP_CT_Rx2dose_NormRx", "env": {"PATH": "${workspaceFolder};${env:PATH}"}, "console": "integratedTerminal", "justMyCode": false}, {"name": "dose3d_predict", "type": "python", "request": "launch", "program": "${workspaceFolder}/predict_simple.py", "args": ["-i", "C:/ARTDaemon/distribute/nnUNETDatav2.dose_GP/nnUNet_raw/Dataset635_GP_CT_Rx2dose_NormRx/imagesTs", "-o", "C:/ARTDaemon/distribute/nnUNETDatav2.dose_GP/nnUNet_raw/Dataset635_GP_CT_Rx2dose_NormRx/predictsTs", "-m", "C:/ARTDaemon/distribute/nnUNETDatav2.dose_GP/nnUNet_trained_models/Dataset635_GP_CT_Rx2dose_NormRx/nnUNetTrainer__nnUNetPlans__3d_fullres", "-f", "0", "-chk", "checkpoint_best.pth", "--disable_tta"], "cwd": "C:/ARTDaemon/distribute/nnUNETDatav2.dose_GP/nnUNet_raw/Dataset635_GP_CT_Rx2dose_NormRx", "env": {"PATH": "${workspaceFolder};${env:PATH}"}, "console": "integratedTerminal", "justMyCode": false}, {"name": "mr2ct_train", "type": "python", "request": "launch", "program": "${workspaceFolder}/run_training.py", "args": ["Dataset450_MNI_MR_T12CT", "3d_fullres", "0", "-tr", "nnUNetTrainer"], "cwd": "C:/ARTDaemon/distribute/nnUNETDatav2.GLIS/nnUNet_raw/Dataset450_MNI_MR_T12CT", "env": {"PATH": "${workspaceFolder};${env:PATH}"}, "console": "integratedTerminal", "justMyCode": false}, {"name": "mr2ct_plan_and_preprocess", "type": "python", "request": "launch", "program": "${workspaceFolder}/plan_and_preprocess_entrypoints.py", "args": ["-d", "450", "-c", "3d_fullres", "--verify_dataset_integrity", "--verbose", "--clean"], "cwd": "C:/ARTDaemon/distribute/nnUNETDatav2.GLIS/nnUNet_raw/Dataset450_MNI_MR_T12CT", "env": {"PATH": "${workspaceFolder};${env:PATH}"}, "console": "integratedTerminal", "justMyCode": false}, {"name": "predict_totalsegv2", "type": "python", "request": "launch", "program": "${workspaceFolder}/predict_simple.py", "args": ["-i", "C:/test/nnunet/Dataset850_TotalSegMRI_part1_organs_1088subj/predict/Input", "-o", "C:/test/nnunet/Dataset850_TotalSegMRI_part1_organs_1088subj/predict/Output", "-m", "C:/ARTDaemon/distribute/nnUNETDatav2.TotalSegmentator/nnUNet_trained_models/Dataset850_TotalSegMRI_part1_organs_1088subj/nnUNetTrainer_2000epochs_NoMirroring__nnUNetPlans__3d_fullres", "-f", "0", "-chk", "checkpoint_final.pth", "-nps", "1", "-npp", "1", "--disable_tta"], "cwd": "C:/ARTDaemon/distribute/nnUNETDatav2.TotalSegmentator/nnUNet_trained_models/Dataset850_TotalSegMRI_part1_organs_1088subj", "env": {"PATH": "${workspaceFolder};${env:PATH}"}, "console": "integratedTerminal", "justMyCode": false}, {"name": "train_Dataset140_Head_CT2Body_4x4x4", "type": "python", "request": "launch", "program": "${workspaceFolder}/run_training.py", "args": ["Dataset140_Head_CT2Body_4x4x4", "3d_fullres", "0", "-tr", "nnUNetTrainer", "--do_input_mask_augmentation", "1", "--mask_diameter_mm", "200", "600", "600", "--mask_diameter_delta_mm", "50", "0", "0", "--mask_shape", "Box", "--mask_include_mode", "0"], "cwd": "C:/ARTDaemon/distribute/nnUNETDatav2.GLIS/nnUNet_raw/Dataset450_MNI_MR_T12CT", "env": {"PATH": "${workspaceFolder};${env:PATH}"}, "console": "integratedTerminal", "justMyCode": false}, {"name": "Run VSMR1Targ_predict", "type": "python", "request": "launch", "program": "${workspaceFolder}/predict_simple.py", "args": ["-i", "c:/test/nnunet/Dataset006_VSMR1Targ/predict/Input/", "-o", "c:/test/nnunet/Dataset006_VSMR1Targ/predict/Output/", "-m", "c:/ARTDaemon/distribute/nnUNETDatav2.MR_VS/nnUNet_trained_models/Dataset006_VSMR1Targ/nnUNetTrainer__nnUNetPlans__3d_fullres", "-f", "0", "-chk", "checkpoint_best.pth", "--disable_tta"], "cwd": "C:/test/nnunet/Dataset006_VSMR1Targ/predict", "env": {"PATH": "${workspaceFolder};${env:PATH}"}, "console": "integratedTerminal", "justMyCode": false}]}