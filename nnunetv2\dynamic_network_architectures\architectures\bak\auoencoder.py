class PlainConvDecoder(UNetDecoder):
    def __init__(self, 
                 encoder: PlainConvEncoder, 
                 num_classes: int, 
                 n_conv_per_stage: Union[int, Tuple[int, ...], List[int]],
                 deep_supervision: bool,
                 nonlin_first: bool = False,
                 norm_op: Union[None, Type[nn.Module]] = None,
                 norm_op_kwargs: dict = None,
                 dropout_op: Union[None, Type[_DropoutNd]] = None,
                 dropout_op_kwargs: dict = None,
                 nonlin: Union[None, Type[torch.nn.Module]] = None,
                 nonlin_kwargs: dict = None,
                 conv_bias: bool = None):
        """
        A simplified decoder derived from UNetDecoder that does not use skip connections.
        """
        super().__init__(encoder, 
                         num_classes, 
                         n_conv_per_stage, 
                         deep_supervision, 
                         nonlin_first, 
                         norm_op, 
                         norm_op_kwargs, 
                         dropout_op, 
                         dropout_op_kwargs, 
                         nonlin, 
                         nonlin_kwargs, 
                         conv_bias)
    
    def forward(self, skips):
        """
        Forward pass that ignores skip connections and only processes features from the bottleneck.
        """
        lres_input = skips[-1]  # Start with the bottleneck features
        seg_outputs = []

        for s in range(len(self.stages)):
            x = self.transpconvs[s](lres_input)  # Transposed convolution to upsample
            x = self.stages[s](x)  # Apply stacked convolutions at this stage

            # Generate segmentation output if using deep supervision
            if self.deep_supervision:
                seg_outputs.append(self.seg_layers[s](x))
            elif s == (len(self.stages) - 1):  # Final segmentation layer
                seg_outputs.append(self.seg_layers[-1](x))
            
            lres_input = x  # Prepare input for the next stage

        seg_outputs = seg_outputs[::-1]  # Reverse outputs to have the largest map first
        return seg_outputs[0] if not self.deep_supervision else seg_outputs

class PlainConvAutoEncoder(PlainConvUNet):
    def __init__(self, 
                 input_channels: int, 
                 base_num_features: int, 
                 num_classes: int, 
                 num_pool: int, 
                 n_conv_per_stage: Union[int, Tuple[int, ...], List[int]], 
                 deep_supervision: bool = False, 
                 nonlin_first: bool = False,
                 norm_op: Union[None, Type[nn.Module]] = None,
                 norm_op_kwargs: dict = None,
                 dropout_op: Union[None, Type[_DropoutNd]] = None,
                 dropout_op_kwargs: dict = None,
                 nonlin: Union[None, Type[torch.nn.Module]] = None,
                 nonlin_kwargs: dict = None,
                 conv_bias: bool = None):
        """
        PlainConvAutoEncoder, derived from PlainConvUNet, designed for autoencoding tasks.
        """
        super().__init__(
            input_channels=input_channels, 
            base_num_features=base_num_features, 
            num_classes=num_classes, 
            num_pool=num_pool, 
            n_conv_per_stage=n_conv_per_stage, 
            deep_supervision=deep_supervision, 
            nonlin_first=nonlin_first, 
            norm_op=norm_op, 
            norm_op_kwargs=norm_op_kwargs, 
            dropout_op=dropout_op, 
            dropout_op_kwargs=dropout_op_kwargs, 
            nonlin=nonlin, 
            nonlin_kwargs=nonlin_kwargs, 
            conv_bias=conv_bias
        )

        # Replace UNetDecoder with PlainConvDecoder
        self.decoder = PlainConvDecoder(
            encoder=self.encoder,  # Use the same encoder as PlainConvUNet
            n_conv_per_stage=n_conv_per_stage,
            deep_supervision=deep_supervision,
            nonlin_first=nonlin_first,
            norm_op=norm_op,
            norm_op_kwargs=norm_op_kwargs,
            dropout_op=dropout_op,
            dropout_op_kwargs=dropout_op_kwargs,
            nonlin=nonlin,
            nonlin_kwargs=nonlin_kwargs,
            conv_bias=conv_bias
        )

    def forward(self, x):
        """
        Forward pass for the autoencoder.
        - The encoder compresses the input to a latent representation.
        - The decoder reconstructs the input from this representation.
        """
        skips = self.encoder(x)  # Encoder: outputs latent features from different levels
        reconstruction = self.decoder(skips)  # Decoder: reconstructs the input
        return reconstruction
