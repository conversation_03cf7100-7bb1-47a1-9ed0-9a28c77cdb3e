import os
import nibabel as nib
import torch
from nnunetv2.training.data_augmentation.custom_transforms.image_mask_transform import ImageMaskTransform

# Config
input_folder = r"C:\ARTDaemon\distribute\nnUNETData.MetalArtifactCT_Head\nnUNet_raw\Dataset532_RecoverAnything_AE_2dCT\1"
output_folder = r"C:\ARTDaemon\distribute\nnUNETData.MetalArtifactCT_Head\nnUNet_raw\Dataset532_RecoverAnything_AE_2dCT\imagesTs_EllipseMask"
os.makedirs(output_folder, exist_ok=True)

# Instantiate your transform
masker = ImageMaskTransform(
    center_mm=[128, 128],
    center_delta_mm=[30, 30],
    diameter_mm=[20, 20],
    diameter_delta_mm=[5, 5],
    mask_shape="Ellipse",
    mask_mode=1,  # mask inside (or 2 for mask outside)
    preprocessed_dataset_folder=r"C:\ARTDaemon\distribute\nnUNETData.MetalArtifactCT_Head\nnUNet_preprocessed\Dataset532_RecoverAnything_AE_2dCT"
)

for fname in os.listdir(input_folder):
    if not fname.endswith(".nii.gz"):
        continue
    img_path = os.path.join(input_folder, fname)
    img_nii = nib.load(img_path)
    img_np = img_nii.get_fdata().astype("float32")  # shape: (H, W) or (H, W, D)
    img_tensor = torch.from_numpy(img_np).float()  # Ensure it's float32
    if img_tensor.ndim == 2:
        img_tensor = img_tensor.unsqueeze(0)

    masked = masker(image=img_tensor)['image'].squeeze().numpy()

    # Save masked image
    masked_nii = nib.Nifti1Image(masked, affine=img_nii.affine, header=img_nii.header)
    nib.save(masked_nii, os.path.join(output_folder, fname))
