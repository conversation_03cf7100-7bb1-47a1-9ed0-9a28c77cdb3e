from typing import Optional, Sequence, Literal
import torch
import numpy as np
import json
import os
from batchgeneratorsv2.transforms.base.basic_transform import ImageOnlyTransform

class ImageMaskTransform(ImageOnlyTransform):
    def __init__(
        self,
        input_mask_channels: int = 0,
        center_mm: Optional[Sequence[int]] = None,
        center_delta_mm: Optional[Sequence[int]] = None,
        diameter_mm: Optional[Sequence[int]] = None,
        diameter_delta_mm: Optional[Sequence[int]] = None,
        mask_shape: Literal["Box", "Ellipse", "Cylinder2D", "Cylinder"] = "Ellipse",
        mask_include_mode: Literal[0, 1] = 1,  # 0=exclude region, 1=include region
        z_mask_mode: Literal[0, 1, 2, 3] = 0,  # 0=no mask, 1=inferior, 2=superior, 3=both (only for 3D image!!)
        z_mask_length_mm: Optional[float] = None,
        z_mask_length_delta_mm: Optional[float] = None,
        mask_intensity_mean: Optional[float] = None,
        mask_intensity_std: Optional[float] = None,
        preprocessed_dataset_folder: Optional[str] = None,
    ):
        super().__init__()
        self.input_mask_channels = input_mask_channels
        self.center_mm = center_mm
        self.center_delta_mm = center_delta_mm
        self.diameter_mm = diameter_mm
        self.diameter_delta_mm = diameter_delta_mm
        self.mask_shape = mask_shape
        self.mask_include_mode = mask_include_mode
        self.z_mask_mode = z_mask_mode
        self.z_mask_length_mm = z_mask_length_mm
        self.z_mask_length_delta_mm = z_mask_length_delta_mm
        self.mask_intensity_mean = mask_intensity_mean
        self.mask_intensity_std = mask_intensity_std

        self.spacing = None
        if preprocessed_dataset_folder is not None:
            fp = os.path.join(preprocessed_dataset_folder, "nnUNetPlans.json")
            with open(fp, 'r') as f:
                spacing = json.load(f)["original_median_spacing_after_transp"]
            self.spacing = spacing

    def convert_mm_to_voxel(self, mm_values: Sequence[float], ndim: int) -> Sequence[int]:
        if self.spacing is None:
            raise ValueError("Spacing must be provided to convert mm to voxel units.")
        spacing = self.spacing[1:] if ndim == 2 and len(self.spacing) == 3 else self.spacing
        return [round(m / s) for m, s in zip(mm_values, spacing[:ndim])]

    def generate_random_mask(self, shape, ndim, mask_include_mode: int = 1):
        """
        Generate a binary mask of shape `shape` using the selected mask shape (Box, Ellipse, etc.),
        with center and diameter randomly perturbed by delta ranges (in voxel units).
        
        Args:
            shape: Shape of the output mask
            ndim: Number of dimensions (2 or 3)
            mask_include_mode: If 1, return the mask as is; if 0, return the inverted mask (~mask)
        """
        center = None
        if self.center_mm is not None:
            image_center = [s // 2 for s in shape]
            center_offset_vox = self.convert_mm_to_voxel(self.center_mm, ndim)
            center = [c + offset for c, offset in zip(image_center, center_offset_vox)]

        diameter = self.convert_mm_to_voxel(self.diameter_mm, ndim) if self.diameter_mm is not None else None
        center_delta = self.convert_mm_to_voxel(self.center_delta_mm, ndim) if self.center_delta_mm is not None else [0 for _ in range(ndim)]
        diameter_delta = self.convert_mm_to_voxel(self.diameter_delta_mm, ndim) if self.diameter_delta_mm is not None else [0 for _ in range(ndim)]

        base_center = center or [s // 2 for s in shape]
        random_center = [
            np.clip(
                base_center[i] + np.random.randint(-center_delta[i], center_delta[i] + 1),
                0, shape[i] - 1
            )
            for i in range(ndim)
        ]

        base_diameter = diameter or [s // 5 for s in shape]
        random_diameter = []
        for i in range(ndim):
            # Allow diameter to be larger than image size
            min_val = max(1, base_diameter[i] - diameter_delta[i])
            max_val = base_diameter[i] + diameter_delta[i] + 1
            
            # If min_val >= max_val, set a deterministic value instead of random
            if min_val >= max_val:
                random_diameter.append(max(1, base_diameter[i]))  # Use base diameter as fallback
            else:
                random_diameter.append(np.random.randint(min_val, max_val))
        
        random_radius = [d // 2 for d in random_diameter]

        if ndim == 2:
            Y, X = np.ogrid[:shape[0], :shape[1]]
            mask = np.zeros(shape, dtype=bool)
            if self.mask_shape == "Box":
                # Use coordinate-based approach instead of slicing
                mask = (
                    (np.abs(Y - random_center[0]) <= random_radius[0]) & 
                    (np.abs(X - random_center[1]) <= random_radius[1])
                )
                
            elif self.mask_shape == "Ellipse" | self.mask_shape == "Cylinder2D" | self.mask_shape == "Cylinder" :
                mask = (((Y - random_center[0]) / random_radius[0]) ** 2 +
                        ((X - random_center[1]) / random_radius[1]) ** 2) <= 1
                
            else:
                raise NotImplementedError("2D only supports 'Box', 'Ellipse', 'Cylinder2D'")
        else:
            Z, Y, X = np.ogrid[:shape[0], :shape[1], :shape[2]]
            mask = np.zeros(shape, dtype=bool)
            if self.mask_shape == "Box":
                # Use coordinate-based approach instead of slicing
                mask = (
                    (np.abs(Z - random_center[0]) <= random_radius[0]) & 
                    (np.abs(Y - random_center[1]) <= random_radius[1]) & 
                    (np.abs(X - random_center[2]) <= random_radius[2])
                )
                
            elif self.mask_shape == "Ellipse":
                mask = (((Z - random_center[0]) / random_radius[0]) ** 2 +
                        ((Y - random_center[1]) / random_radius[1]) ** 2 +
                        ((X - random_center[2]) / random_radius[2]) ** 2) <= 1
            
            elif self.mask_shape == "Cylinder2D":
                mask = (((Y - random_center[1]) / random_radius[1]) ** 2 + 
                        ((X - random_center[2]) / random_radius[2]) ** 2 <= 1) 
                
            elif self.mask_shape == "Cylinder":
                mask = (((Y - random_center[1]) / random_radius[1]) ** 2 + 
                        ((X - random_center[2]) / random_radius[2]) ** 2 <= 1) & \
                       (np.abs(Z - random_center[0]) <= random_radius[0])
                
            else:
                raise NotImplementedError("3D only supports 'Box', 'Ellipse', 'Cylinder2D', 'Cylinder'")

        # Invert mask if mask_include_mode is 0
        if mask_include_mode == 0:
            mask = ~mask

        return mask


    def get_nonzero_z_bounds(self, img: torch.Tensor) -> tuple[int, int]:
        """
        Find first and last z-slices with non-zero voxels.
        Assumes img shape is (C, Z, Y, X)
        """
        nonzero_mask = torch.any(img != 0, dim=(0, 2, 3))
        nonzero_indices = torch.where(nonzero_mask)[0]
        if len(nonzero_indices) == 0:
            return 0, img.shape[1] - 1
        return nonzero_indices[0].item(), nonzero_indices[-1].item()

    def apply_random_z_mask(self, img):
        ndim = 3
        z_start, z_end = self.get_nonzero_z_bounds(img)
        roi_depth = z_end - z_start + 1  # total usable slices

        # noise-zmask
        mean_val = img.mean()
        std_val = img.std()

        if self.z_mask_mode in [1, 2, 3] and self.z_mask_length_mm is not None:
            # Convert mask length to voxel
            z_length_mm = self.z_mask_length_mm
            if not isinstance(z_length_mm, (list, tuple)):
                z_length_mm = [z_length_mm]
            z_vox = self.convert_mm_to_voxel(z_length_mm, ndim=ndim)[0]

            # Apply jitter if specified
            jitter_vox = 0
            if self.z_mask_length_delta_mm is not None:
                delta_mm = self.z_mask_length_delta_mm
                if not isinstance(delta_mm, (list, tuple)):
                    delta_mm = [delta_mm]
                delta_vox = self.convert_mm_to_voxel(delta_mm, ndim=ndim)[0]
                jitter_vox = np.random.randint(-delta_vox, delta_vox + 1)

            z_vox = np.clip(z_vox + jitter_vox, a_min=0, a_max=roi_depth)
            scale = 0.1
            if self.z_mask_mode == 1:
                z_slice = slice(z_start, z_start + z_vox)
                noise = torch.randn_like(img[:, z_slice, :, :]) * (scale * std_val) + mean_val
                img[:, z_slice, :, :] = noise
            elif self.z_mask_mode == 2:
                z_slice = slice(z_end - z_vox + 1, z_end + 1)
                noise = torch.randn_like(img[:, z_slice, :, :]) * (scale * std_val) + mean_val
                img[:, z_slice, :, :] = noise
            elif self.z_mask_mode == 3:
                if len(z_length_mm) != 2:
                    raise ValueError("z_mask_length_mm must be a list of 2 values for z_mask_mode == 3")
                z_start_mm, z_end_mm = z_length_mm
                z_start_vox = self.convert_mm_to_voxel([z_start_mm], ndim=ndim)[0]
                z_end_vox = self.convert_mm_to_voxel([z_end_mm], ndim=ndim)[0]

                # Apply jitter to both ends
                if isinstance(self.z_mask_length_delta_mm, (list, tuple)) and len(self.z_mask_length_delta_mm) == 2:
                    delta_start_mm, delta_end_mm = self.z_mask_length_delta_mm
                    delta_start_vox = self.convert_mm_to_voxel([delta_start_mm], ndim=ndim)[0]
                    delta_end_vox = self.convert_mm_to_voxel([delta_end_mm], ndim=ndim)[0]
                    jitter_start = np.random.randint(-delta_start_vox, delta_start_vox + 1)
                    jitter_end = np.random.randint(-delta_end_vox, delta_end_vox + 1)
                    z_start_vox = np.clip(z_start_vox + jitter_start, 0, roi_depth)
                    z_end_vox = np.clip(z_end_vox + jitter_end, 0, roi_depth)

                start_slice = slice(z_start, z_start + z_start_vox)
                end_slice = slice(z_end - z_end_vox + 1, z_end + 1)
                noise_start = torch.randn_like(img[:, start_slice, :, :]) * (scale * std_val) + mean_val
                noise_end = torch.randn_like(img[:, end_slice, :, :]) * (scale * std_val) + mean_val
                img[:, start_slice, :, :] = noise_start
                img[:, end_slice, :, :] = noise_end

        return img

    def _apply_to_image(self, img: torch.Tensor, **params) -> torch.Tensor:
        ndim = img.ndim - 1
        shape = img.shape[1:]

        if self.spacing is None:
            raise ValueError("Spacing must be provided to convert mm to voxel units.")

        for c in range(img.shape[0]):
            if self.input_mask_channels & (1 << c):
                # Generate mask
                mask = self.generate_random_mask(shape, ndim, self.mask_include_mode)
                mask = torch.from_numpy(mask).to(img.device)
                
                # Calculate mean and std of the image
                img_mean_val = img[c].mean()
                img_std_val = img[c].std()
                
                # Use custom intensity values if provided, otherwise use image statistics
                mean_val = self.mask_intensity_mean if self.mask_intensity_mean is not None else img_mean_val
                std_val = self.mask_intensity_std if self.mask_intensity_std is not None else img_std_val * 0.1  # 10% of image std by default
                
                # Generate noise for masked region
                noise = torch.randn_like(img[c]) * std_val + mean_val
                
                # Apply mask: keep original values in non-masked region, replace with noise in masked region
                img[c] = torch.where(mask, noise, img[c])

                # Z-direction mask
                if ndim == 3 and self.z_mask_mode > 0:
                    img[c:c + 1] = self.apply_random_z_mask(img[c:c + 1])

        return img
