from typing import Optional, Sequence, Literal
import torch
import numpy as np
import json
import os
from batchgeneratorsv2.transforms.base.basic_transform import ImageOnlyTransform

class ImageMaskTransform(ImageOnlyTransform):
    def __init__(
        self,
        center_mm: Optional[Sequence[int]] = None,
        center_delta_mm: Optional[Sequence[int]] = None,
        diameter_mm: Optional[Sequence[int]] = None,
        diameter_delta_mm: Optional[Sequence[int]] = None,
        mask_shape: Literal["Box", "Ellipse", "EllipseCylinder"] = "Ellipse",
        mask_mode: Literal[0, 1, 2] = 0,
<<<<<<< working copy
        z_mask_mode: Literal[0, 1, 2, 3] = 0,  # 0=no mask, 1=mask start→end, 2=end→start, 3=both (only for 3D image!!)
        z_mask_length_mm: Optional[float] = None,
        z_mask_length_delta_mm: Optional[float] = None,
=======
>>>>>>> destination
        preprocessed_dataset_folder: Optional[str] = None,
    ):
        super().__init__()
        self.center_mm = center_mm
        self.center_delta_mm = center_delta_mm
        self.diameter_mm = diameter_mm
        self.diameter_delta_mm = diameter_delta_mm
        self.mask_shape = mask_shape
        self.mask_mode = mask_mode
<<<<<<< working copy
        self.z_mask_mode = z_mask_mode
        self.z_mask_length_mm = z_mask_length_mm
        self.z_mask_length_delta_mm = z_mask_length_delta_mm
=======
>>>>>>> destination

        self.spacing = None
        if preprocessed_dataset_folder is not None:
            fp = os.path.join(preprocessed_dataset_folder, "dataset_fingerprint.json")
            with open(fp, 'r') as f:
                spacing = json.load(f)["spacings"][0]
            self.spacing = spacing

    def convert_mm_to_voxel(self, mm_values: Sequence[float], ndim: int) -> Sequence[int]:
        if self.spacing is None:
            raise ValueError("Spacing must be provided to convert mm to voxel units.")
        spacing = self.spacing[1:] if ndim == 2 and len(self.spacing) == 3 else self.spacing
        return [round(m / s) for m, s in zip(mm_values, spacing[:ndim])]


    def _apply_to_image(self, img: torch.Tensor, **params) -> torch.Tensor:
        ndim = img.ndim - 1  # exclude channel
        shape = img.shape[1:]

        if self.spacing is None:
            raise ValueError("Spacing must be provided to convert mm to voxel units.")

        # Convert mm values to voxel space
        center = self.convert_mm_to_voxel(self.center_mm, ndim) if self.center_mm is not None else None
        diameter = self.convert_mm_to_voxel(self.diameter_mm, ndim) if self.diameter_mm is not None else None
        center_delta = self.convert_mm_to_voxel(self.center_delta_mm, ndim) if self.center_delta_mm is not None else [0 for _ in range(ndim)]
        diameter_delta = self.convert_mm_to_voxel(self.diameter_delta_mm, ndim) if self.diameter_delta_mm is not None else [0 for _ in range(ndim)]

        base_center = center or [s // 2 for s in shape]
        random_center = [
            np.clip(
                base_center[i] + np.random.randint(-center_delta[i], center_delta[i] + 1),
                0, shape[i] - 1
            )
            for i in range(ndim)
        ]

        base_diameter = diameter or [s // 5 for s in shape]
        random_diameter = [
            np.random.randint(
                max(1, base_diameter[i] - diameter_delta[i]),
                min(shape[i], base_diameter[i] + diameter_delta[i] + 1)
            )
            for i in range(ndim)
        ]
        random_radius = [d // 2 for d in random_diameter]

        # Build mask
        if ndim == 2:
            Y, X = np.ogrid[:shape[0], :shape[1]]
            mask = np.zeros(shape, dtype=bool)

            if self.mask_shape == "Box":
                mask[
                max(random_center[0] - random_radius[0], 0): min(random_center[0] + random_radius[0], shape[0]),
                max(random_center[1] - random_radius[1], 0): min(random_center[1] + random_radius[1], shape[1])
                ] = True
            elif self.mask_shape == "Ellipse":
                mask = (
                               ((Y - random_center[0]) / random_radius[0]) ** 2 +
                               ((X - random_center[1]) / random_radius[1]) ** 2
                       ) <= 1
            else:
                raise NotImplementedError("2D only supports 'Box' and 'Ellipse'")

        elif ndim == 3:
            Z, Y, X = np.ogrid[:shape[0], :shape[1], :shape[2]]
            mask = np.zeros(shape, dtype=bool)

            if self.mask_shape == "Box":
                mask[
                max(random_center[0] - random_radius[0], 0): min(random_center[0] + random_radius[0], shape[0]),
                max(random_center[1] - random_radius[1], 0): min(random_center[1] + random_radius[1], shape[1]),
                max(random_center[2] - random_radius[2], 0): min(random_center[2] + random_radius[2], shape[2])
                ] = True
            elif self.mask_shape == "Ellipse":
                mask = (
                               ((Z - random_center[0]) / random_radius[0]) ** 2 +
                               ((Y - random_center[1]) / random_radius[1]) ** 2 +
                               ((X - random_center[2]) / random_radius[2]) ** 2
                       ) <= 1
            elif self.mask_shape == "EllipseCylinder":
                ellipse_mask = (
                                       ((Y - random_center[1]) / random_radius[1]) ** 2 +
                                       ((X - random_center[2]) / random_radius[2]) ** 2
                               ) <= 1
                ellipse_mask = ellipse_mask[np.newaxis, :, :]  # ensure shape (1, H, W)
                mask = np.broadcast_to(ellipse_mask, shape)
            else:
<<<<<<< working copy
                raise NotImplementedError("3D only supports 'Box', 'Ellipse', 'EllipseCylinder'")

        return mask

    def apply_random_z_mask(self, img, shape):
        ndim = 3

        if self.z_mask_mode in [1, 2, 3] and self.z_mask_length_mm is not None:
            # Convert z_mask_length_mm to voxel
            z_length_mm = self.z_mask_length_mm
            if not isinstance(z_length_mm, (list, tuple)):
                z_length_mm = [z_length_mm]
            z_vox = self.convert_mm_to_voxel(z_length_mm, ndim=ndim)[0]
=======
                raise NotImplementedError("3D only supports 'Box', 'Ellipse', and 'EllipseCylinder'")
        else:
            raise ValueError("Unsupported dimension")
>>>>>>> destination

<<<<<<< working copy
            # Apply jitter if delta is given
            jitter_vox = 0
            if self.z_mask_length_delta_mm is not None:
                delta_mm = self.z_mask_length_delta_mm
                if not isinstance(delta_mm, (list, tuple)):
                    delta_mm = [delta_mm]
                delta_vox = self.convert_mm_to_voxel(delta_mm, ndim=ndim)[0]
                jitter_vox = np.random.randint(-delta_vox, delta_vox + 1)
=======
        mask = torch.from_numpy(mask).to(img.device).unsqueeze(0)
>>>>>>> destination

<<<<<<< working copy
            z_vox = np.clip(z_vox + jitter_vox, a_min=0, a_max=shape[0])

            # Apply one-sided z-masking
            if self.z_mask_mode == 1:
                img[:, :z_vox, :, :] = 0  # mask from start
            elif self.z_mask_mode == 2:
                img[:, -z_vox:, :, :] = 0  # mask from end

            # Apply two-sided z-masking
            elif self.z_mask_mode == 3:
                if len(z_length_mm) != 2:
                    raise ValueError("z_mask_length_mm must be a list or tuple of 2 values when z_mask_mode == 3")

                z_start_mm, z_end_mm = z_length_mm
                z_start_vox = self.convert_mm_to_voxel([z_start_mm], ndim=ndim)[0]
                z_end_vox = self.convert_mm_to_voxel([z_end_mm], ndim=ndim)[0]

                # Jitter for both sides
                if isinstance(self.z_mask_length_delta_mm, (list, tuple)) and len(self.z_mask_length_delta_mm) == 2:
                    delta_start_mm, delta_end_mm = self.z_mask_length_delta_mm
                    delta_start_vox = self.convert_mm_to_voxel([delta_start_mm], ndim=ndim)[0]
                    delta_end_vox = self.convert_mm_to_voxel([delta_end_mm], ndim=ndim)[0]
                    jitter_start = np.random.randint(-delta_start_vox, delta_start_vox + 1)
                    jitter_end = np.random.randint(-delta_end_vox, delta_end_vox + 1)
                    z_start_vox = np.clip(z_start_vox + jitter_start, a_min=0, a_max=shape[0])
                    z_end_vox = np.clip(z_end_vox + jitter_end, a_min=0, a_max=shape[0])

                img[:, :z_start_vox, :, :] = 0
                img[:, -z_end_vox:, :, :] = 0
=======
        if self.mask_mode == 1:
            img = img * (~mask)
        elif self.mask_mode == 2:
            img = img * mask
>>>>>>> destination

        return img

<<<<<<< working copy
    def _apply_to_image(self, img: torch.Tensor, **params) -> torch.Tensor:
        ndim = img.ndim - 1
        shape = img.shape[1:]

        if self.spacing is None:
            raise ValueError("Spacing must be provided to convert mm to voxel units.")

        # adding mask: ellipse, box, et al.
        mask = self.generate_random_mask(shape, ndim)
        mask = torch.from_numpy(mask).to(img.device).unsqueeze(0)
        if self.mask_mode == 1:
            img = img * (~mask)
        elif self.mask_mode == 2:
            img = img * mask

        # adding z-mask
        if ndim == 3 and self.z_mask_mode > 0:
            img = self.apply_random_z_mask(img, shape)

        return img=======
>>>>>>> destination
