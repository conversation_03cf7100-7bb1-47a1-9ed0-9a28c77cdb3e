import torch
from nnunetv2.training.loss.dice import SoftDiceLoss, MemoryEfficientSoftDiceLoss, MemoryEfficientMSELoss
from nnunetv2.training.loss.robust_ce_loss import RobustCrossEntropyLoss, TopKLoss
from nnunetv2.utilities.helpers import softmax_helper_dim1
from torch import nn

# Define the Huber loss function
class Huber_loss(nn.Module):
    def __init__(self, huber_kwargs, delta=1000):
        """
        Initialize the Huber Loss function.
        Args:
            delta (float): The threshold at which to switch from quadratic to linear loss.
        """
        super(Huber_loss, self).__init__()
        self.delta = delta

    def forward(self, predictions, targets):
        """
        Calculate the Huber loss.
        Args:
            predictions (torch.Tensor): Predicted values.
            targets (torch.Tensor): Ground truth values.
        Returns:
            torch.Tensor: Calculated Huber loss.
        """
        diff = predictions - targets
        abs_diff = torch.abs(diff)
        quadratic = torch.where(abs_diff <= self.delta, 0.5 * diff ** 2, self.delta * abs_diff - 0.5 * self.delta ** 2)
        return quadratic.mean()
    
class DC_and_CE_loss(nn.Module):
    def __init__(self, soft_dice_kwargs, ce_kwargs, weight_ce=1, weight_dice=1, ignore_label=None,
                 dice_class=SoftDiceLoss):
        """
        Weights for CE and Dice do not need to sum to one. You can set whatever you want.
        :param soft_dice_kwargs:
        :param ce_kwargs:
        :param aggregate:
        :param square_dice:
        :param weight_ce:
        :param weight_dice:
        """
        super(DC_and_CE_loss, self).__init__()
        if ignore_label is not None:
            ce_kwargs['ignore_index'] = ignore_label

        self.weight_dice = weight_dice
        self.weight_ce = weight_ce
        self.ignore_label = ignore_label

        self.ce = RobustCrossEntropyLoss(**ce_kwargs)
        self.dc = dice_class(apply_nonlin=softmax_helper_dim1, **soft_dice_kwargs)

    def forward(self, net_output: torch.Tensor, target: torch.Tensor):
        """
        target must be b, c, x, y(, z) with c=1
        :param net_output:
        :param target:
        :return:
        """
        if self.ignore_label is not None:
            assert target.shape[1] == 1, 'ignore label is not implemented for one hot encoded target variables ' \
                                         '(DC_and_CE_loss)'
            mask = target != self.ignore_label
            # remove ignore label from target, replace with one of the known labels. It doesn't matter because we
            # ignore gradients in those areas anyway
            target_dice = torch.where(mask, target, 0)
            num_fg = mask.sum()
        else:
            target_dice = target
            mask = None

        dc_loss = self.dc(net_output, target_dice, loss_mask=mask) \
            if self.weight_dice != 0 else 0
        ce_loss = self.ce(net_output, target[:, 0]) \
            if self.weight_ce != 0 and (self.ignore_label is None or num_fg > 0) else 0

        result = self.weight_ce * ce_loss + self.weight_dice * dc_loss
        return result


class DC_and_BCE_loss(nn.Module):
    def __init__(self, bce_kwargs, soft_dice_kwargs, weight_ce=1, weight_dice=1, use_ignore_label: bool = False,
                 dice_class=MemoryEfficientSoftDiceLoss):
        """
        DO NOT APPLY NONLINEARITY IN YOUR NETWORK!

        target mut be one hot encoded
        IMPORTANT: We assume use_ignore_label is located in target[:, -1]!!!

        :param soft_dice_kwargs:
        :param bce_kwargs:
        :param aggregate:
        """
        super(DC_and_BCE_loss, self).__init__()
        if use_ignore_label:
            bce_kwargs['reduction'] = 'none'

        self.weight_dice = weight_dice
        self.weight_ce = weight_ce
        self.use_ignore_label = use_ignore_label

        self.ce = nn.BCEWithLogitsLoss(**bce_kwargs)
        self.dc = dice_class(apply_nonlin=torch.sigmoid, **soft_dice_kwargs)

    def forward(self, net_output: torch.Tensor, target: torch.Tensor):
        if self.use_ignore_label:
            # target is one hot encoded here. invert it so that it is True wherever we can compute the loss
            if target.dtype == torch.bool:
                mask = ~target[:, -1:]
            else:
                mask = (1 - target[:, -1:]).bool()
            # remove ignore channel now that we have the mask
            # why did we use clone in the past? Should have documented that...
            # target_regions = torch.clone(target[:, :-1])
            target_regions = target[:, :-1]
        else:
            target_regions = target
            mask = None

        dc_loss = self.dc(net_output, target_regions, loss_mask=mask)
        target_regions = target_regions.float()
        if mask is not None:
            ce_loss = (self.ce(net_output, target_regions) * mask).sum() / torch.clip(mask.sum(), min=1e-8)
        else:
            ce_loss = self.ce(net_output, target_regions)
        result = self.weight_ce * ce_loss + self.weight_dice * dc_loss
        return result

class MSE_loss(nn.Module):
    def __init__(self, mse_kwargs):
        super(MSE_loss, self).__init__()
        self.mse = MemoryEfficientMSELoss(**mse_kwargs)
 
    def forward(self, net_output: torch.Tensor, target_regions: torch.Tensor):

        mse_loss = self.mse(net_output, target_regions)
        # print(result)
        return mse_loss
    
class DC_and_MSE_loss(nn.Module):
    def __init__(self, mse_kwargs, soft_dice_kwargs, weight_mse=1, weight_dice=0, use_ignore_label: bool = False,
                 dice_class=MemoryEfficientSoftDiceLoss):
        """
        DO NOT APPLY NONLINEARITY IN YOUR NETWORK!
        target must be one hot encoded for Dice loss
        IMPORTANT: We assume use_ignore_label is located in target[:, -1]!!!
        :param mse_kwargs: Arguments for MSE loss
        :param soft_dice_kwargs: Arguments for Dice loss
        :param weight_mse: Weight for MSE loss
        :param weight_dice: Weight for Dice loss
        :param use_ignore_label: Boolean indicating whether to use ignore label
        :param dice_class: Dice loss class
        """
        super(DC_and_MSE_loss, self).__init__()
        if use_ignore_label:
            mse_kwargs['reduction'] = 'none'
        self.weight_dice = weight_dice
        self.weight_mse = weight_mse
        print("mse_loss_weight",self.weight_mse)
        self.use_ignore_label = use_ignore_label
        self.mse = MemoryEfficientMSELoss(**mse_kwargs)
        self.dc = dice_class(apply_nonlin=torch.sigmoid, **soft_dice_kwargs)

    def forward(self, net_output: torch.Tensor, target: torch.Tensor):
        # print('forward')
        # print(self.use_ignore_label)
        # print(net_output.shape)
        # print(target.shape)
        # print(torch.max(target[:,0,:,:]))
        # sys.exit(0)
        if self.use_ignore_label:
            # target is one hot encoded here. invert it so that it is True wherever we can compute the loss
            if target.dtype == torch.bool:
                mask = ~target[:, -1:]
            else:
                mask = (1 - target[:, -1:]).bool()
            # remove ignore channel now that we have the mask
            target_regions = target[:, :-1]
        else:
            target_regions = target
            mask = None
        # print(net_output[:,-1,:,:,:].unsqueeze(1).shape)
        # print(target_regions[:,-1,:,:,:].unsqueeze(1).shape)
        # print(net_output[:,:-1,:,:,:].shape)
        # print(target_regions[:,0,:,:,:].unsqueeze(1).shape)
        # sys.exit(0)
        # dc_loss = self.dc(net_output, target_regions, loss_mask=mask)
        # print(torch.max(target_regions[:,0,:,:]))

        if self.weight_dice != 0:
            dc_loss = self.dc(net_output[:,:-1,:,:,:], target_regions[:,0,:,:,:].unsqueeze(1), loss_mask=mask)
        # target_regions = target_regions.float()
        # dc_loss = self.dc(net_output[:,:-1,:,:], target_regions[:,0,:,:].unsqueeze(1), loss_mask=mask)
        # dc_loss = self.dc(net_output[:,0,:,:], target_regions[:,0,:,:], loss_mask=mask)
        target_regions = target_regions.float()
        # print(mask)

        if mask is not None:
            mse_loss = (self.mse(net_output, target_regions) * mask).sum() / torch.clip(mask.sum(), min=1e-8)
        else:
            # mse_loss = self.mse(net_output, target_regions[:,1,:,:])
            #mse_loss = self.mse(net_output[:,-1,:,:,:], target_regions[:,-1,:,:,:])
            mse_loss = (self.mse(net_output, target_regions)).sum()
            
        if self.weight_dice != 0:
            result = self.weight_mse*mse_loss + self.weight_dice*dc_loss
        else:
            result = self.weight_mse*mse_loss
        # print(result)
        return result

class DC_and_topk_loss(nn.Module):
    def __init__(self, soft_dice_kwargs, ce_kwargs, weight_ce=1, weight_dice=1, ignore_label=None):
        """
        Weights for CE and Dice do not need to sum to one. You can set whatever you want.
        :param soft_dice_kwargs:
        :param ce_kwargs:
        :param aggregate:
        :param square_dice:
        :param weight_ce:
        :param weight_dice:
        """
        super().__init__()
        if ignore_label is not None:
            ce_kwargs['ignore_index'] = ignore_label

        self.weight_dice = weight_dice
        self.weight_ce = weight_ce
        self.ignore_label = ignore_label

        self.ce = TopKLoss(**ce_kwargs)
        self.dc = SoftDiceLoss(apply_nonlin=softmax_helper_dim1, **soft_dice_kwargs)

    def forward(self, net_output: torch.Tensor, target: torch.Tensor):
        """
        target must be b, c, x, y(, z) with c=1
        :param net_output:
        :param target:
        :return:
        """
        if self.ignore_label is not None:
            assert target.shape[1] == 1, 'ignore label is not implemented for one hot encoded target variables ' \
                                         '(DC_and_CE_loss)'
            mask = (target != self.ignore_label).bool()
            # remove ignore label from target, replace with one of the known labels. It doesn't matter because we
            # ignore gradients in those areas anyway
            target_dice = torch.clone(target)
            target_dice[target == self.ignore_label] = 0
            num_fg = mask.sum()
        else:
            target_dice = target
            mask = None

        dc_loss = self.dc(net_output, target_dice, loss_mask=mask) \
            if self.weight_dice != 0 else 0
        ce_loss = self.ce(net_output, target) \
            if self.weight_ce != 0 and (self.ignore_label is None or num_fg > 0) else 0

        result = self.weight_ce * ce_loss + self.weight_dice * dc_loss
        return result


class DC_and_CE_and_local_loss(nn.Module):
    def __init__(self, soft_dice_kwargs, ce_kwargs, weight_ce=1, weight_dice1=0.6, weight_dice2=0.4, ignore_label=None,
                 dice_class=SoftDiceLoss):
        """
        Weights for CE and Dice do not need to sum to one. You can set whatever you want.
        :param soft_dice_kwargs:
        :param ce_kwargs:
        :param aggregate:
        :param square_dice:
        :param weight_ce:
        :param weight_dice:
        """
        super(DC_and_CE_and_local_loss, self).__init__()
        if ignore_label is not None:
            ce_kwargs['ignore_index'] = ignore_label

        self.weight_dice1 = weight_dice1
        self.weight_dice2 = weight_dice2
        self.weight_ce = weight_ce
        self.ignore_label = ignore_label

        self.ce = RobustCrossEntropyLoss(**ce_kwargs)
        self.dc = dice_class(apply_nonlin=softmax_helper_dim1, **soft_dice_kwargs)
        
    def forward(self, net_output: torch.Tensor, target: torch.Tensor):
        """
        target must be b, c, x, y(, z) with c=1
        :param net_output:
        :param target:
        :return:
        """
        if self.ignore_label is not None:
            assert target.shape[1] == 1, 'ignore label is not implemented for one hot encoded target variables ' \
                                         '(DC_and_CE_loss)'
            mask = target != self.ignore_label
            # remove ignore label from target, replace with one of the known labels. It doesn't matter because we
            # ignore gradients in those areas anyway
            target_dice = torch.where(mask, target, 0)
            num_fg = mask.sum()
        else:
            target_dice = target
            mask = None
        target_dice = target_dice.to(torch.float16)
        dc_loss1 = self.dc(net_output, target_dice, loss_mask=mask)
        dc_loss2 = self.dc(nn.functional.max_pool3d(net_output,kernel_size=(8,8,8)), nn.functional.max_pool3d(target_dice,kernel_size=(8,8,8)), loss_mask=mask)
                
        ce_loss = self.ce(net_output, target[:, 0]) \
            if self.weight_ce != 0 and (self.ignore_label is None or num_fg > 0) else 0

        result = self.weight_ce * ce_loss + self.weight_dice1 * dc_loss1 + self.weight_dice2 * dc_loss2
        return result
    

class SSL_NCEinfo_loss(nn.Module):
    def __init__(self, bce_kwargs, soft_dice_kwargs, weight_ce=1, weight_dice=1, use_ignore_label: bool = False,
                 dice_class=MemoryEfficientSoftDiceLoss):
        """
        DO NOT APPLY NONLINEARITY IN YOUR NETWORK!

        target mut be one hot encoded
        IMPORTANT: We assume use_ignore_label is located in target[:, -1]!!!

        :param soft_dice_kwargs:
        :param bce_kwargs:
        :param aggregate:
        """
        super(DC_and_BCE_loss, self).__init__()
        if use_ignore_label:
            bce_kwargs['reduction'] = 'none'

        self.weight_dice = weight_dice
        self.weight_ce = weight_ce
        self.use_ignore_label = use_ignore_label

        self.ce = nn.BCEWithLogitsLoss(**bce_kwargs)
        self.dc = dice_class(apply_nonlin=torch.sigmoid, **soft_dice_kwargs)

    def forward(self, net_output: torch.Tensor, target: torch.Tensor):
        if self.use_ignore_label:
            # target is one hot encoded here. invert it so that it is True wherever we can compute the loss
            if target.dtype == torch.bool:
                mask = ~target[:, -1:]
            else:
                mask = (1 - target[:, -1:]).bool()
            # remove ignore channel now that we have the mask
            # why did we use clone in the past? Should have documented that...
            # target_regions = torch.clone(target[:, :-1])
            target_regions = target[:, :-1]
        else:
            target_regions = target
            mask = None

        dc_loss = self.dc(net_output, target_regions, loss_mask=mask)
        target_regions = target_regions.float()
        if mask is not None:
            ce_loss = (self.ce(net_output, target_regions) * mask).sum() / torch.clip(mask.sum(), min=1e-8)
        else:
            ce_loss = self.ce(net_output, target_regions)
        result = self.weight_ce * ce_loss + self.weight_dice * dc_loss
        return result    